package main

import (
	"fmt"
	"math"
	"strings"
	"time"
)

// <PERSON><PERSON> represents different Islamic schools of thought for Asr calculation
type <PERSON><PERSON> int

const (
	<PERSON><PERSON><PERSON> = iota // Single shadow length
	Hanafi              // Double shadow length
)

// CalculationMethod represents different calculation methods
type CalculationMethod struct {
	Name         string
	FajrAngle    float64
	IshaAngle    float64
	IshaInterval int // minutes after Maghrib (0 if using angle)
}

// Common calculation methods based on the Flutter code analysis
var (
	MuslimWorldLeague = CalculationMethod{"Muslim World League", 18.0, 17.0, 0}
	Egyptian          = CalculationMethod{"Egyptian", 19.5, 17.7, 0}
	Karachi           = CalculationMethod{"Karachi", 18.0, 18.0, 0}
	UmmAlQura         = CalculationMethod{"Umm Al-Qura", 18.5, 0, 90}
	Dubai             = CalculationMethod{"Dubai", 18.2, 18.2, 0}
	NorthAmerica      = CalculationMethod{"North America", 15.0, 15.0, 0}
	Kuwait            = CalculationMethod{"Kuwait", 18.0, 17.5, 0}
	Singapore         = CalculationMethod{"Singapore", 20.0, 18.0, 0}
)

// Coordinates represents geographical coordinates
type Coordinates struct {
	Latitude  float64
	Longitude float64
}

// PrayerTimes holds all prayer times for a day
type PrayerTimes struct {
	Fajr    time.Time
	Sunrise time.Time
	Dhuha   time.Time // 上午祈祷 - 日出后太阳升高4.5度
	Dhuhr   time.Time
	Asr     time.Time
	Maghrib time.Time
	Isha    time.Time
}

// SolarCalculator handles astronomical calculations
type SolarCalculator struct {
	date        time.Time
	coordinates Coordinates
}

// NewSolarCalculator creates a new calculator
func NewSolarCalculator(date time.Time, coordinates Coordinates) *SolarCalculator {
	return &SolarCalculator{
		date:        date,
		coordinates: coordinates,
	}
}

// julianDay calculates the Julian day number
func (sc *SolarCalculator) julianDay() float64 {
	year := sc.date.Year()
	month := int(sc.date.Month())
	day := sc.date.Day()
	
	if month <= 2 {
		year--
		month += 12
	}
	
	a := year / 100
	b := 2 - a + a/4
	
	return float64(int(365.25*float64(year+4716)) + int(30.6001*float64(month+1)) + day + b - 1524)
}

// solarPosition calculates solar declination and equation of time
func (sc *SolarCalculator) solarPosition() (declination, eot float64) {
	jd := sc.julianDay()
	n := jd - 2451545.0
	
	// Mean longitude of the sun
	L := math.Mod(280.460+0.9856474*n, 360.0)
	
	// Mean anomaly
	g := math.Mod(357.528+0.9856003*n, 360.0)
	
	// Ecliptic longitude
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)
	
	// Declination
	declination = math.Asin(math.Sin(degToRad(23.439)) * math.Sin(degToRad(lambda)))
	
	// Equation of time
	eot = 4 * (L - 0.0057183 - math.Atan2(math.Tan(degToRad(lambda)), math.Cos(degToRad(23.439))))
	
	return declination, eot
}

// transitTime calculates solar transit (noon) time
func (sc *SolarCalculator) transitTime() float64 {
	_, eot := sc.solarPosition()
	return 12 - sc.coordinates.Longitude/15 - eot/60
}

// timeForAngle calculates time when sun is at specific angle below/above horizon
func (sc *SolarCalculator) timeForAngle(angle float64, afterTransit bool) float64 {
	declination, eot := sc.solarPosition()
	
	// Hour angle calculation
	cosHourAngle := (math.Sin(degToRad(angle)) - 
		math.Sin(degToRad(sc.coordinates.Latitude))*math.Sin(declination)) /
		(math.Cos(degToRad(sc.coordinates.Latitude)) * math.Cos(declination))
	
	if cosHourAngle < -1 || cosHourAngle > 1 {
		return math.NaN() // Sun doesn't reach this angle
	}
	
	hourAngle := math.Acos(cosHourAngle)
	transit := 12 - sc.coordinates.Longitude/15 - eot/60
	
	if afterTransit {
		return transit + radToDeg(hourAngle)/15
	}
	return transit - radToDeg(hourAngle)/15
}

// asrTime calculates Asr prayer time based on shadow length
func (sc *SolarCalculator) asrTime(madhab Madhab) float64 {
	declination, eot := sc.solarPosition()
	
	// Shadow length factor: 1 for Shafi, 2 for Hanafi
	shadowFactor := 1.0
	if madhab == Hanafi {
		shadowFactor = 2.0
	}
	
	// Calculate solar altitude for Asr
	// When shadow length = shadowFactor * object height + noon shadow
	latRad := degToRad(sc.coordinates.Latitude)
	
	// Solar altitude at Asr
	asrAltitude := math.Atan(1.0 / (shadowFactor + math.Tan(math.Abs(latRad-declination))))
	asrAngle := 90 - radToDeg(asrAltitude)
	
	return sc.timeForAngle(-asrAngle, true)
}

// CalculatePrayerTimes calculates all prayer times for a given date and location
func CalculatePrayerTimes(date time.Time, coordinates Coordinates, method CalculationMethod, madhab Madhab) *PrayerTimes {
	calc := NewSolarCalculator(date, coordinates)
	
	// Helper function to convert decimal hours to time.Time
	hoursToTime := func(hours float64) time.Time {
		if math.IsNaN(hours) {
			return time.Time{}
		}
		
		// Ensure hours is in valid range [0, 24)
		hours = math.Mod(hours+24, 24)
		
		h := int(hours)
		m := int((hours - float64(h)) * 60)
		s := int(((hours-float64(h))*60 - float64(m)) * 60)
		
		return time.Date(date.Year(), date.Month(), date.Day(), h, m, s, 0, date.Location())
	}
	
	// Calculate each prayer time
	fajrTime := calc.timeForAngle(-method.FajrAngle, false)
	sunriseTime := calc.timeForAngle(0, false) // 0 degrees = horizon
	dhuhaTime := calc.timeForAngle(4.5, false) // Dhuha: 4.5 degrees above horizon
	dhuhrTime := calc.transitTime()             // Solar noon
	asrTime := calc.asrTime(madhab)            // Based on shadow length
	maghribTime := calc.timeForAngle(0, true)  // Sunset
	
	var ishaTime float64
	if method.IshaInterval > 0 {
		// Use time interval after Maghrib (e.g., Umm Al-Qura method)
		ishaTime = maghribTime + float64(method.IshaInterval)/60.0
	} else {
		// Use angle below horizon
		ishaTime = calc.timeForAngle(-method.IshaAngle, true)
	}
	
	return &PrayerTimes{
		Fajr:    hoursToTime(fajrTime),
		Sunrise: hoursToTime(sunriseTime),
		Dhuha:   hoursToTime(dhuhaTime),
		Dhuhr:   hoursToTime(dhuhrTime),
		Asr:     hoursToTime(asrTime),
		Maghrib: hoursToTime(maghribTime),
		Isha:    hoursToTime(ishaTime),
	}
}

// Helper functions
func degToRad(degrees float64) float64 {
	return degrees * math.Pi / 180.0
}

func radToDeg(radians float64) float64 {
	return radians * 180.0 / math.Pi
}

// FormatTime formats time in 24-hour format
func FormatTime(t time.Time) string {
	if t.IsZero() {
		return "N/A"
	}
	return t.Format("15:04")
}

// PrintPrayerTimes prints formatted prayer times
func PrintPrayerTimes(times *PrayerTimes, location string, method CalculationMethod, madhab Madhab) {
	madhabName := "Shafi"
	if madhab == Hanafi {
		madhabName = "Hanafi"
	}
	
	fmt.Printf("\n=== Prayer Times for %s ===\n", location)
	fmt.Printf("Method: %s | Madhab: %s\n", method.Name, madhabName)
	fmt.Printf("Fajr Angle: %.1f° | Isha: ", method.FajrAngle)
	if method.IshaInterval > 0 {
		fmt.Printf("%d min after Maghrib\n", method.IshaInterval)
	} else {
		fmt.Printf("%.1f°\n", method.IshaAngle)
	}
	fmt.Println(strings.Repeat("-", 40))
	
	fmt.Printf("Fajr (晨礼):    %s\n", FormatTime(times.Fajr))
	fmt.Printf("Sunrise (日出): %s\n", FormatTime(times.Sunrise))
	fmt.Printf("Dhuha (上午):   %s  (日出后4.5°)\n", FormatTime(times.Dhuha))
	fmt.Printf("Dhuhr (晌礼):   %s  (正午)\n", FormatTime(times.Dhuhr))
	fmt.Printf("Asr (晡礼):     %s  (影长%s)\n", FormatTime(times.Asr), 
		map[Madhab]string{Shafi: "1倍", Hanafi: "2倍"}[madhab])
	fmt.Printf("Maghrib (昏礼): %s  (日落)\n", FormatTime(times.Maghrib))
	fmt.Printf("Isha (宵礼):    %s\n", FormatTime(times.Isha))
}

func main() {
	// Test locations
	locations := map[string]Coordinates{
		"麦加 (Mecca)":     {21.4225, 39.8262},
		"开罗 (Cairo)":     {30.0444, 31.2357},
		"伊斯坦布尔 (Istanbul)": {41.0082, 28.9784},
		"雅加达 (Jakarta)":   {-6.2088, 106.8456},
		"北京 (Beijing)":    {39.9042, 116.4074},
	}
	
	// Current date
	date := time.Now()
	fmt.Printf("计算日期: %s\n", date.Format("2006-01-02"))
	
	// Test different calculation methods
	methods := []CalculationMethod{MuslimWorldLeague, Egyptian, Karachi, UmmAlQura}
	
	for city, coords := range locations {
		for _, method := range methods {
			times := CalculatePrayerTimes(date, coords, method, Shafi)
			PrintPrayerTimes(times, city, method, Shafi)
		}
		fmt.Println()
	}
	
	// Demonstrate Madhab difference for Asr
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("Asr时间的教法学派差异 (以麦加为例):")
	fmt.Println(strings.Repeat("=", 60))
	
	mecca := locations["麦加 (Mecca)"]
	
	shafiTimes := CalculatePrayerTimes(date, mecca, MuslimWorldLeague, Shafi)
	hanafiTimes := CalculatePrayerTimes(date, mecca, MuslimWorldLeague, Hanafi)
	
	fmt.Printf("Shafi学派 (影长=物高×1):  Asr %s\n", FormatTime(shafiTimes.Asr))
	fmt.Printf("Hanafi学派 (影长=物高×2): Asr %s\n", FormatTime(hanafiTimes.Asr))
	
	// Calculate time difference
	diff := hanafiTimes.Asr.Sub(shafiTimes.Asr)
	fmt.Printf("时间差: %.0f 分钟\n", diff.Minutes())
}
