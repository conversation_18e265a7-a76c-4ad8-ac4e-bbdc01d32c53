package main

import (
	"fmt"
	"math"
	"strings"
	"time"
)

// Prayer represents different prayer types
type Prayer int

const (
	Fajr Prayer = iota
	Sunrise
	Dhuha
	Dhuhr
	<PERSON>r
	<PERSON>
)

// <PERSON><PERSON> represents different Islamic schools of thought
type <PERSON><PERSON> int

const (
	<PERSON><PERSON><PERSON> = iota // Single shadow length
	Hanafi               // Double shadow length
)

// CalculationMethod represents different calculation methods
type CalculationMethod struct {
	Name         string
	FajrAngle    float64
	IshaAngle    float64
	IshaInterval int // minutes after <PERSON>gh<PERSON><PERSON> (0 if using angle)
	MaghribAngle float64
}

// Common calculation methods
var (
	MuslimWorldLeague     = CalculationMethod{"Muslim World League", 18.0, 17.0, 0, 0}
	Egyptian              = CalculationMethod{"Egyptian", 19.5, 17.7, 0, 0}
	Karachi               = CalculationMethod{"Karachi", 18.0, 18.0, 0, 0}
	UmmAlQura             = CalculationMethod{"Umm Al-Qura", 18.5, 0, 90, 0}
	Dubai                 = CalculationMethod{"Dubai", 18.2, 18.2, 0, 0}
	MoonsightingCommittee = CalculationMethod{"Moonsighting Committee", 18.0, 18.0, 0, 0}
	NorthAmerica          = CalculationMethod{"North America", 15.0, 15.0, 0, 0}
	Kuwait                = CalculationMethod{"Kuwait", 18.0, 17.5, 0, 0}
	Qatar                 = CalculationMethod{"Qatar", 18.0, 0, 90, 0}
	Singapore             = CalculationMethod{"Singapore", 20.0, 18.0, 0, 0}
)

// Coordinates represents geographical coordinates
type Coordinates struct {
	Latitude  float64
	Longitude float64
}

// PrayerTimes holds all prayer times for a day
type PrayerTimes struct {
	Fajr    time.Time
	Sunrise time.Time
	Dhuha   time.Time
	Dhuhr   time.Time
	Asr     time.Time
	Maghrib time.Time
	Isha    time.Time
}

// SolarTime handles astronomical calculations
type SolarTime struct {
	date        time.Time
	coordinates Coordinates
	transit     float64
	sunrise     float64
	sunset      float64
}

// NewSolarTime creates a new SolarTime instance
func NewSolarTime(date time.Time, coordinates Coordinates) *SolarTime {
	st := &SolarTime{
		date:        date,
		coordinates: coordinates,
	}
	st.calculate()
	return st
}

// calculate performs the main solar calculations with improved precision
func (st *SolarTime) calculate() {
	// Julian day calculation
	jd := st.julianDay()

	// More precise solar calculations based on Flutter code
	n := jd - 2451545.0

	// Mean longitude of the sun (degrees)
	L := math.Mod(280.460+0.9856474*n, 360.0)

	// Mean anomaly (degrees)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	gRad := degToRad(g)

	// Ecliptic longitude (degrees) with higher order terms
	lambda := math.Mod(L+1.915*math.Sin(gRad)+0.020*math.Sin(2*gRad), 360.0)
	lambdaRad := degToRad(lambda)

	// Obliquity of the ecliptic (more precise)
	obliquity := 23.439 - 0.0000004*n
	obliquityRad := degToRad(obliquity)

	// Solar declination
	declination := math.Asin(math.Sin(obliquityRad) * math.Sin(lambdaRad))

	// Right ascension
	rightAscension := math.Atan2(math.Cos(obliquityRad)*math.Sin(lambdaRad), math.Cos(lambdaRad))

	// Equation of time (minutes)
	eot := 4 * (L - 0.0057183 - radToDeg(rightAscension))
	if eot > 20 {
		eot -= 1440
	} else if eot < -20 {
		eot += 1440
	}

	// Hour angle for sunrise/sunset with atmospheric refraction correction
	// Standard refraction is about 0.833 degrees (50 arcminutes)
	refraction := 0.833
	cosHourAngle := (math.Sin(degToRad(-refraction)) -
		math.Sin(degToRad(st.coordinates.Latitude))*math.Sin(declination)) /
		(math.Cos(degToRad(st.coordinates.Latitude)) * math.Cos(declination))

	if cosHourAngle < -1 || cosHourAngle > 1 {
		// Polar day/night - sun doesn't rise/set
		st.transit = 12 - st.coordinates.Longitude/15 - eot/60
		st.sunrise = math.NaN()
		st.sunset = math.NaN()
		return
	}

	hourAngle := math.Acos(cosHourAngle)

	// Transit, sunrise, sunset in decimal hours
	st.transit = 12 - st.coordinates.Longitude/15 - eot/60
	st.sunrise = st.transit - radToDeg(hourAngle)/15
	st.sunset = st.transit + radToDeg(hourAngle)/15
}

// julianDay calculates the Julian day number
func (st *SolarTime) julianDay() float64 {
	year := st.date.Year()
	month := int(st.date.Month())
	day := st.date.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	return float64(int(365.25*float64(year+4716)) + int(30.6001*float64(month+1)) + day + b - 1524)
}

// hourAngleForAngle calculates hour angle for a given solar angle
func (st *SolarTime) hourAngleForAngle(angle float64, afterTransit bool) float64 {
	// Simplified calculation - in real implementation, this would be more complex
	jd := st.julianDay()
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	declination := math.Asin(math.Sin(degToRad(23.439)) * math.Sin(degToRad(lambda)))

	// Hour angle calculation for the given angle
	cosHourAngle := (math.Sin(degToRad(angle)) - math.Sin(degToRad(st.coordinates.Latitude))*math.Sin(declination)) /
		(math.Cos(degToRad(st.coordinates.Latitude)) * math.Cos(declination))

	if cosHourAngle < -1 || cosHourAngle > 1 {
		return math.NaN() // Sun doesn't reach this angle
	}

	hourAngle := math.Acos(cosHourAngle)

	if afterTransit {
		return st.transit + radToDeg(hourAngle)/15
	}
	return st.transit - radToDeg(hourAngle)/15
}

// asrTime calculates Asr prayer time based on shadow length
func (st *SolarTime) asrTime(madhab Madhab) float64 {
	// Shadow length factor: 1 for Shafi, 2 for Hanafi
	shadowFactor := 1.0
	if madhab == Hanafi {
		shadowFactor = 2.0
	}

	// Simplified Asr calculation
	// In reality, this involves more complex shadow length calculations
	jd := st.julianDay()
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	declination := math.Asin(math.Sin(degToRad(23.439)) * math.Sin(degToRad(lambda)))

	// Calculate solar altitude for Asr
	asrAltitude := math.Atan(1.0 / (shadowFactor + math.Tan(math.Abs(degToRad(st.coordinates.Latitude)-declination))))
	asrAngle := 90 - radToDeg(asrAltitude)

	return st.hourAngleForAngle(-asrAngle, true)
}

// CalculatePrayerTimes calculates all prayer times for a given date and location
func CalculatePrayerTimes(date time.Time, coordinates Coordinates, method CalculationMethod, madhab Madhab, timezone *time.Location) *PrayerTimes {
	// Use UTC for calculations to avoid timezone issues
	utcDate := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)
	solarTime := NewSolarTime(utcDate, coordinates)

	// Helper function to convert decimal hours to time.Time in specified timezone
	hoursToTime := func(hours float64) time.Time {
		if math.IsNaN(hours) {
			return time.Time{}
		}

		// Ensure hours is in valid range
		hours = math.Mod(hours+24, 24)

		h := int(hours)
		m := int((hours - float64(h)) * 60)
		s := int(((hours-float64(h))*60 - float64(m)) * 60)

		// Create time in UTC first, then convert to target timezone
		utcTime := time.Date(utcDate.Year(), utcDate.Month(), utcDate.Day(), h, m, s, 0, time.UTC)
		return utcTime.In(timezone)
	}

	// Calculate each prayer time
	fajrTime := solarTime.hourAngleForAngle(-method.FajrAngle, false)
	sunriseTime := solarTime.sunrise
	dhuhaTime := solarTime.hourAngleForAngle(4.5, false) // Dhuha is 4.5 degrees after sunrise
	dhuhrTime := solarTime.transit
	asrTime := solarTime.asrTime(madhab)
	maghribTime := solarTime.sunset

	var ishaTime float64
	if method.IshaInterval > 0 {
		// Use time interval after Maghrib
		ishaTime = maghribTime + float64(method.IshaInterval)/60.0
	} else {
		// Use angle
		ishaTime = solarTime.hourAngleForAngle(-method.IshaAngle, true)
	}

	return &PrayerTimes{
		Fajr:    hoursToTime(fajrTime),
		Sunrise: hoursToTime(sunriseTime),
		Dhuha:   hoursToTime(dhuhaTime),
		Dhuhr:   hoursToTime(dhuhrTime),
		Asr:     hoursToTime(asrTime),
		Maghrib: hoursToTime(maghribTime),
		Isha:    hoursToTime(ishaTime),
	}
}

func main() {
	// Example usage with timezone support
	fmt.Println("请输入时区 (例如: Asia/Riyadh, Asia/Shanghai, Europe/London):")
	fmt.Print("时区: ")
	var timezoneStr string
	fmt.Scanln(&timezoneStr)

	// Default to Asia/Riyadh if empty
	if timezoneStr == "" {
		timezoneStr = "Asia/Riyadh"
	}

	timezone, err := time.LoadLocation(timezoneStr)
	if err != nil {
		fmt.Printf("无效的时区，使用默认时区 Asia/Riyadh: %v\n", err)
		timezone, _ = time.LoadLocation("Asia/Riyadh")
	}

	// Coordinates for Mecca, Saudi Arabia
	mecca := Coordinates{Latitude: 21.4225, Longitude: 39.8262}

	// Current date
	date := time.Now()

	// Calculate prayer times using Muslim World League method with Shafi madhab
	prayerTimes := CalculatePrayerTimes(date, mecca, MuslimWorldLeague, Shafi, timezone)

	fmt.Printf("\nPrayer Times for %s (Mecca) - %s\n", date.Format("2006-01-02"), timezone.String())
	fmt.Printf("Method: %s, Madhab: Shafi\n\n", MuslimWorldLeague.Name)

	fmt.Printf("Fajr (晨礼):    %s\n", FormatTime(prayerTimes.Fajr))
	fmt.Printf("Sunrise (日出): %s\n", FormatTime(prayerTimes.Sunrise))
	fmt.Printf("Dhuha (上午):   %s  (日出后4.5°)\n", FormatTime(prayerTimes.Dhuha))
	fmt.Printf("Dhuhr (晌礼):   %s\n", FormatTime(prayerTimes.Dhuhr))
	fmt.Printf("Asr (晡礼):     %s\n", FormatTime(prayerTimes.Asr))
	fmt.Printf("Maghrib (昏礼): %s\n", FormatTime(prayerTimes.Maghrib))
	fmt.Printf("Isha (宵礼):    %s\n", FormatTime(prayerTimes.Isha))

	fmt.Println("\n" + strings.Repeat("=", 60))

	// Test different locations and methods
	locations := map[string]struct {
		coords Coordinates
		tz     string
	}{
		"麦加 (Mecca)":       {Coordinates{21.4225, 39.8262}, "Asia/Riyadh"},
		"开罗 (Cairo)":       {Coordinates{30.0444, 31.2357}, "Africa/Cairo"},
		"雅加达 (Jakarta)":    {Coordinates{-6.2088, 106.8456}, "Asia/Jakarta"},
		"伊斯坦布尔 (Istanbul)": {Coordinates{41.0082, 28.9784}, "Europe/Istanbul"},
		"北京 (Beijing)":     {Coordinates{39.9042, 116.4074}, "Asia/Shanghai"},
	}

	methods := []CalculationMethod{MuslimWorldLeague, Egyptian, Karachi, UmmAlQura}

	for city, info := range locations {
		cityTz, _ := time.LoadLocation(info.tz)
		fmt.Printf("\n%s (%+.4f, %+.4f) - %s:\n", city, info.coords.Latitude, info.coords.Longitude, info.tz)

		for _, method := range methods {
			times := CalculatePrayerTimes(date, info.coords, method, Shafi, cityTz)
			fmt.Printf("  %s: Fajr %s, Dhuhr %s, Asr %s, Maghrib %s, Isha %s\n",
				method.Name,
				FormatTime(times.Fajr),
				FormatTime(times.Dhuhr),
				FormatTime(times.Asr),
				FormatTime(times.Maghrib),
				FormatTime(times.Isha))
		}
	}

	// Demonstrate Madhab difference for Asr
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("Asr时间的教法学派差异 (以麦加为例):")
	fmt.Println(strings.Repeat("=", 60))

	shafiTimes := CalculatePrayerTimes(date, mecca, MuslimWorldLeague, Shafi, timezone)
	hanafiTimes := CalculatePrayerTimes(date, mecca, MuslimWorldLeague, Hanafi, timezone)

	fmt.Printf("Shafi学派 (影长=物高×1):  Asr %s\n", FormatTime(shafiTimes.Asr))
	fmt.Printf("Hanafi学派 (影长=物高×2): Asr %s\n", FormatTime(hanafiTimes.Asr))

	// Calculate time difference
	diff := hanafiTimes.Asr.Sub(shafiTimes.Asr)
	fmt.Printf("时间差: %.0f 分钟\n", diff.Minutes())
}
