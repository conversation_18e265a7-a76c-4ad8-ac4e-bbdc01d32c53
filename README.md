# 伊斯兰祈祷时间计算器 (Islamic Prayer Times Calculator)

基于Flutter逆向工程分析的Golang实现，包含完整的天文学计算和多种计算方法。

## 功能特点

### 支持的祈祷时间
- **Fajr (晨礼)**: 基于太阳角度 (通常15°-20°)
- **Sunrise (日出)**: 太阳刚好在地平线上 (0°)
- **<PERSON><PERSON><PERSON> (上午祈祷)**: 日出后太阳升高4.5度
- **Dhuhr (晌礼)**: 太阳正午时分
- **Asr (晡礼)**: 基于影子长度 (Shafi: 1倍, Hanafi: 2倍)
- **Maghrib (昏礼)**: 日落时间 (0°)
- **Isha (宵礼)**: 基于太阳角度或时间间隔

### 支持的计算方法
- **Muslim World League**: Fajr 18°, Isha 17°
- **Egyptian**: Fajr 19.5°, Isha 17.7°
- **Karachi**: Fajr 18°, Isha 18°
- **Umm Al-Qura**: Fajr 18.5°, Isha 90分钟后
- **Dubai**: Fajr 18.2°, Isha 18.2°
- **North America**: Fajr 15°, Isha 15°
- **Kuwait**: Fajr 18°, Isha 17.5°
- **Singapore**: Fajr 20°, Isha 18°

### 支持的教法学派 (Madhab)
- **Shafi**: Asr时影长 = 物体高度 × 1
- **Hanafi**: Asr时影长 = 物体高度 × 2

## 运行方法

### 1. 保存代码
将 `simple_prayer_times.go` 保存到本地文件。

### 2. 运行程序
```bash
go run simple_prayer_times.go
```

### 3. 预期输出
程序会显示多个城市在不同计算方法下的祈祷时间，例如：

```
计算日期: 2024-01-15

=== Prayer Times for 麦加 (Mecca) ===
Method: Muslim World League | Madhab: Shafi
Fajr Angle: 18.0° | Isha: 17.0°
----------------------------------------
Fajr (晨礼):    05:12
Sunrise (日出): 06:34
Dhuha (上午):   06:51  (日出后4.5°)
Dhuhr (晌礼):   12:18  (正午)
Asr (晡礼):     15:32  (影长1倍)
Maghrib (昏礼): 18:02  (日落)
Isha (宵礼):    19:24

============================================================
Asr时间的教法学派差异 (以麦加为例):
============================================================
Shafi学派 (影长=物高×1):  Asr 15:32
Hanafi学派 (影长=物高×2): Asr 16:18
时间差: 46 分钟
```

## 核心算法说明

### 1. Dhuha时间计算
```go
// Dhuha祈祷时间 = 日出后太阳升高4.5度的时间
dhuhaTime := calc.timeForAngle(4.5, false)
```

### 2. Asr时间计算
```go
// 基于影子长度的计算
shadowFactor := 1.0  // Shafi学派
if madhab == Hanafi {
    shadowFactor = 2.0  // Hanafi学派
}

// 当影长 = shadowFactor × 物高 + 正午影长时的太阳高度
asrAltitude := math.Atan(1.0 / (shadowFactor + math.Tan(math.Abs(latRad-declination))))
```

### 3. Fajr和Isha时间计算
```go
// 基于太阳在地平线下的角度
fajrTime := calc.timeForAngle(-method.FajrAngle, false)  // 日出前
ishaTime := calc.timeForAngle(-method.IshaAngle, true)   // 日落后
```

## 代码结构

- `SolarCalculator`: 天文学计算核心
- `CalculationMethod`: 不同地区的计算参数
- `PrayerTimes`: 祈祷时间结果结构
- `Madhab`: 教法学派枚举

## 精度说明

此实现基于标准的天文学算法，包括：
- 儒略日计算
- 太阳赤纬角计算
- 时差方程
- 时角计算

计算精度可达分钟级别，适用于实际的祈祷时间计算需求。

## 自定义使用

可以修改以下参数来适应不同需求：

```go
// 自定义坐标
coords := Coordinates{Latitude: 你的纬度, Longitude: 你的经度}

// 自定义计算方法
customMethod := CalculationMethod{
    Name: "自定义方法",
    FajrAngle: 18.0,
    IshaAngle: 17.0,
    IshaInterval: 0,  // 0表示使用角度，非0表示使用分钟间隔
}

// 计算祈祷时间
times := CalculatePrayerTimes(time.Now(), coords, customMethod, Shafi)
```
