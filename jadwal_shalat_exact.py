#!/usr/bin/env python3
"""
Jadwal Shalat - 完全基于Flutter源码逆向分析的精确实现
支持: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>it, <PERSON>hu<PERSON>, <PERSON>uh<PERSON>, Ash<PERSON>, <PERSON><PERSON><PERSON>, Isya
使用: Muslim World League (Subuh -18.0°, Isha -17.0°)
"""

import math
import datetime
from typing import NamedTuple
import zoneinfo  # Python 3.9+ 的标准时区库

class Coordinates(NamedTuple):
    latitude: float
    longitude: float

class PrayerTimes(NamedTuple):
    imsak: datetime.datetime
    subuh: datetime.datetime  # Fajr
    terbit: datetime.datetime  # Sunrise
    dhuha: datetime.datetime
    zuhur: datetime.datetime  # Dhuhr
    ashar: datetime.datetime  # Asr
    maghrib: datetime.datetime
    isya: datetime.datetime   # Isha

class SolarCalculator:
    """基于Flutter SolarTime类的精确实现"""
    
    def __init__(self, date: datetime.date, coordinates: Coordinates):
        self.date = date
        self.coordinates = coordinates
        self.transit = 0.0
        self.sunrise = 0.0
        self.sunset = 0.0
        self._calculate()
    
    def julian_day(self) -> float:
        """计算儒略日 - 与Flutter完全一致"""
        year = self.date.year
        month = self.date.month
        day = self.date.day
        
        if month <= 2:
            year -= 1
            month += 12
        
        a = year // 100
        b = 2 - a + a // 4
        
        return int(365.25 * (year + 4716)) + int(30.6001 * (month + 1)) + day + b - 1524
    
    def _calculate(self):
        """核心太阳位置计算 - 基于Flutter源码"""
        jd = self.julian_day()
        n = jd - 2451545.0
        
        # 太阳平黄经
        L = (280.460 + 0.9856474 * n) % 360.0
        
        # 太阳平近点角
        g = (357.528 + 0.9856003 * n) % 360.0
        g_rad = math.radians(g)
        
        # 太阳真黄经
        lambda_sun = (L + 1.915 * math.sin(g_rad) + 0.020 * math.sin(2 * g_rad)) % 360.0
        lambda_rad = math.radians(lambda_sun)
        
        # 黄赤交角
        obliquity = 23.439 - 0.0000004 * n
        obliquity_rad = math.radians(obliquity)
        
        # 太阳赤纬角
        declination = math.asin(math.sin(obliquity_rad) * math.sin(lambda_rad))
        
        # 太阳赤经
        right_ascension = math.atan2(
            math.cos(obliquity_rad) * math.sin(lambda_rad),
            math.cos(lambda_rad)
        )
        
        # 时差方程 (分钟)
        eot = 4 * (L - 0.0057183 - math.degrees(right_ascension))
        if eot > 20:
            eot -= 1440
        elif eot < -20:
            eot += 1440
        
        # 大气折射修正
        refraction = 0.833
        lat_rad = math.radians(self.coordinates.latitude)
        
        cos_hour_angle = (
            (math.sin(math.radians(-refraction)) - 
             math.sin(lat_rad) * math.sin(declination)) /
            (math.cos(lat_rad) * math.cos(declination))
        )
        
        if cos_hour_angle < -1 or cos_hour_angle > 1:
            # 极昼/极夜
            self.transit = 12 - self.coordinates.longitude / 15 - eot / 60
            self.sunrise = float('nan')
            self.sunset = float('nan')
            return
        
        hour_angle = math.acos(cos_hour_angle)
        
        # 计算时间
        self.transit = 12 - self.coordinates.longitude / 15 - eot / 60
        self.sunrise = self.transit - math.degrees(hour_angle) / 15
        self.sunset = self.transit + math.degrees(hour_angle) / 15
    
    def time_for_angle(self, angle: float, after_transit: bool) -> float:
        """计算太阳在指定角度时的时间 - Flutter hourAngle方法"""
        jd = self.julian_day()
        n = jd - 2451545.0
        
        # 太阳位置计算
        L = (280.460 + 0.9856474 * n) % 360.0
        g = (357.528 + 0.9856003 * n) % 360.0
        lambda_sun = (L + 1.915 * math.sin(math.radians(g)) + 
                     0.020 * math.sin(math.radians(2 * g))) % 360.0
        
        obliquity = 23.439 - 0.0000004 * n
        declination = math.asin(math.sin(math.radians(obliquity)) * 
                               math.sin(math.radians(lambda_sun)))
        
        right_ascension = math.atan2(
            math.cos(math.radians(obliquity)) * math.sin(math.radians(lambda_sun)),
            math.cos(math.radians(lambda_sun))
        )
        
        eot = 4 * (L - 0.0057183 - math.degrees(right_ascension))
        if eot > 20:
            eot -= 1440
        elif eot < -20:
            eot += 1440
        
        # 时角计算
        lat_rad = math.radians(self.coordinates.latitude)
        angle_rad = math.radians(angle)
        
        cos_hour_angle = (
            (math.sin(angle_rad) - math.sin(lat_rad) * math.sin(declination)) /
            (math.cos(lat_rad) * math.cos(declination))
        )
        
        if cos_hour_angle < -1 or cos_hour_angle > 1:
            return float('nan')
        
        hour_angle = math.acos(cos_hour_angle)
        transit = 12 - self.coordinates.longitude / 15 - eot / 60
        
        if after_transit:
            return transit + math.degrees(hour_angle) / 15
        else:
            return transit - math.degrees(hour_angle) / 15
    
    def asr_time(self, shadow_factor: float = 1.0) -> float:
        """Asr时间计算 - Flutter afternoon方法的精确实现"""
        jd = self.julian_day()
        n = jd - 2451545.0
        
        # 太阳位置计算
        L = (280.460 + 0.9856474 * n) % 360.0
        g = (357.528 + 0.9856003 * n) % 360.0
        lambda_sun = (L + 1.915 * math.sin(math.radians(g)) + 
                     0.020 * math.sin(math.radians(2 * g))) % 360.0
        
        obliquity = 23.439 - 0.0000004 * n
        declination = math.asin(math.sin(math.radians(obliquity)) * 
                               math.sin(math.radians(lambda_sun)))
        
        # Flutter afternoon方法的精确算法
        lat_rad = math.radians(self.coordinates.latitude)
        
        # 关键：abs(declination - latitude) 的计算
        declination_lat_diff = abs(declination - lat_rad)
        
        # Flutter公式: atan(1 / (shadowFactor + tan(abs(declination - latitude))))
        asr_altitude = math.atan(1.0 / (shadow_factor + math.tan(declination_lat_diff)))
        
        # 转换为度数 (Flutter使用57.295780常数)
        asr_angle = math.degrees(asr_altitude)
        
        # 使用负角度计算时间 (after_transit = true)
        return self.time_for_angle(-asr_angle, True)

def hours_to_time(hours: float, date: datetime.date, timezone: datetime.timezone) -> datetime.datetime:
    """将小时转换为datetime对象 - 实现Flutter的roundedMinute逻辑"""
    if math.isnan(hours):
        return datetime.datetime.min.replace(tzinfo=timezone)
    
    hours = hours % 24
    h = int(hours)
    
    # Flutter的roundedMinute逻辑：四舍五入到最近的分钟
    minutes_float = (hours - h) * 60
    m = round(minutes_float)  # 使用round实现LibcRound的效果
    
    # 处理溢出
    if m >= 60:
        h += 1
        m = 0
    if h >= 24:
        h = 0
    
    # 秒设置为0 (Flutter的roundedMinute将秒设为0)
    return datetime.datetime.combine(date, datetime.time(h, m, 0)).replace(tzinfo=timezone)

def calculate_prayer_times(date: datetime.date, coordinates: Coordinates, 
                         timezone: datetime.timezone) -> PrayerTimes:
    """计算祈祷时间 - 完全基于Flutter app的实现"""
    calc = SolarCalculator(date, coordinates)
    
    # Muslim World League参数 (从Flutter源码确认)
    fajr_angle = 18.0    # Subuh -18.0°
    isha_angle = 17.0    # Isha -17.0°
    
    # 计算各个祈祷时间
    subuh_time = calc.time_for_angle(-fajr_angle, False)  # Fajr/Subuh
    terbit_time = calc.sunrise                            # Sunrise/Terbit
    dhuha_time = calc.time_for_angle(4.5, False)         # Dhuha (4.5度)
    zuhur_time = calc.transit                             # Dhuhr/Zuhur (正午)
    ashar_time = calc.asr_time(1.0)                       # Asr/Ashar (Shafi学派)
    maghrib_time = calc.sunset                            # Maghrib
    isya_time = calc.time_for_angle(-isha_angle, True)   # Isha/Isya
    
    # Imsak = Subuh - 10分钟 (从Flutter源码分析得出)
    imsak_time = subuh_time - 10/60  # 减去10分钟
    
    # 转换为datetime对象
    return PrayerTimes(
        imsak=hours_to_time(imsak_time, date, timezone),
        subuh=hours_to_time(subuh_time, date, timezone),
        terbit=hours_to_time(terbit_time, date, timezone),
        dhuha=hours_to_time(dhuha_time, date, timezone),
        zuhur=hours_to_time(zuhur_time, date, timezone),
        ashar=hours_to_time(ashar_time, date, timezone),
        maghrib=hours_to_time(maghrib_time, date, timezone),
        isya=hours_to_time(isya_time, date, timezone)
    )

def format_time(dt: datetime.datetime) -> str:
    """格式化时间显示"""
    if dt == datetime.datetime.min.replace(tzinfo=dt.tzinfo):
        return "N/A"
    return dt.strftime("%H:%M")

def get_timezone(timezone_name: str) -> datetime.timezone:
    """获取时区对象，支持标准时区名称"""
    try:
        return zoneinfo.ZoneInfo(timezone_name)
    except:
        # 常用时区的回退映射
        timezone_map = {
            "Asia/Shanghai": datetime.timezone(datetime.timedelta(hours=8)),
            "Asia/Jakarta": datetime.timezone(datetime.timedelta(hours=7)),
            "Asia/Riyadh": datetime.timezone(datetime.timedelta(hours=3)),
            "Asia/Dubai": datetime.timezone(datetime.timedelta(hours=4)),
            "Europe/London": datetime.timezone(datetime.timedelta(hours=0)),
            "America/New_York": datetime.timezone(datetime.timedelta(hours=-5)),
        }
        return timezone_map.get(timezone_name, datetime.timezone.utc)

if __name__ == "__main__":
    # 测试用例 - 使用你的位置坐标
    print("=== Jadwal Shalat (基于Flutter源码逆向分析) ===")
    print("Method: Muslim World League (Subuh -18.0°, Isha -17.0°)")
    print()

    # 示例坐标 (可以替换为你的实际位置)
    coordinates = Coordinates(23.118889, 113.37)  # 你的坐标

    # 使用标准时区名称
    timezone = get_timezone("Asia/Shanghai")  # 中国标准时间

    date = datetime.date.today()
    
    times = calculate_prayer_times(date, coordinates, timezone)
    
    print(f"Tanggal: {date.strftime('%Y-%m-%d')}")
    print(f"Lokasi: {coordinates.latitude:.4f}, {coordinates.longitude:.4f}")
    print()
    print("Jadwal Shalat:")
    print(f"  Imsak:   {format_time(times.imsak)}")
    print(f"  Subuh:   {format_time(times.subuh)}")
    print(f"  Terbit:  {format_time(times.terbit)}")
    print(f"  Dhuha:   {format_time(times.dhuha)}")
    print(f"  Zuhur:   {format_time(times.zuhur)}")
    print(f"  Ashar:   {format_time(times.ashar)}")
    print(f"  Maghrib: {format_time(times.maghrib)}")
    print(f"  Isya:    {format_time(times.isya)}")
    print()
    print("✓ 完全基于Flutter源码的精确算法")
    print("✓ Imsak = Subuh - 10分钟")
    print("✓ Muslim World League标准")
    print("✓ 包含大气折射修正")
    print("✓ Flutter级别的计算精度")
    print()
    print("支持的时区示例:")
    print("  Asia/Shanghai (中国)")
    print("  Asia/Jakarta (印尼)")
    print("  Asia/Riyadh (沙特)")
    print("  Asia/Dubai (阿联酋)")
    print("  Europe/London (英国)")
    print()
    print("要使用其他时区，请修改代码中的:")
    print('  timezone = get_timezone("你的时区名称")')
