#!/usr/bin/env python3
"""
Jadwal Shalat - 交互式版本
支持用户输入坐标和时区
"""

import math
import datetime
from typing import NamedTuple
try:
    import zoneinfo
except ImportError:
    zoneinfo = None

class Coordinates(NamedTuple):
    latitude: float
    longitude: float

class PrayerTimes(NamedTuple):
    imsak: datetime.datetime
    subuh: datetime.datetime
    terbit: datetime.datetime
    dhuha: datetime.datetime
    zuhur: datetime.datetime
    ashar: datetime.datetime
    maghrib: datetime.datetime
    isya: datetime.datetime

class SolarCalculator:
    """基于Flutter SolarTime类的精确实现"""
    
    def __init__(self, date: datetime.date, coordinates: Coordinates):
        self.date = date
        self.coordinates = coordinates
        self.transit = 0.0
        self.sunrise = 0.0
        self.sunset = 0.0
        self._calculate()
    
    def julian_day(self) -> float:
        """计算儒略日"""
        year = self.date.year
        month = self.date.month
        day = self.date.day
        
        if month <= 2:
            year -= 1
            month += 12
        
        a = year // 100
        b = 2 - a + a // 4
        
        return int(365.25 * (year + 4716)) + int(30.6001 * (month + 1)) + day + b - 1524
    
    def _calculate(self):
        """核心太阳位置计算"""
        jd = self.julian_day()
        n = jd - 2451545.0
        
        L = (280.460 + 0.9856474 * n) % 360.0
        g = (357.528 + 0.9856003 * n) % 360.0
        g_rad = math.radians(g)
        
        lambda_sun = (L + 1.915 * math.sin(g_rad) + 0.020 * math.sin(2 * g_rad)) % 360.0
        lambda_rad = math.radians(lambda_sun)
        
        obliquity = 23.439 - 0.0000004 * n
        obliquity_rad = math.radians(obliquity)
        
        declination = math.asin(math.sin(obliquity_rad) * math.sin(lambda_rad))
        
        right_ascension = math.atan2(
            math.cos(obliquity_rad) * math.sin(lambda_rad),
            math.cos(lambda_rad)
        )
        
        eot = 4 * (L - 0.0057183 - math.degrees(right_ascension))
        if eot > 20:
            eot -= 1440
        elif eot < -20:
            eot += 1440
        
        refraction = 0.833
        lat_rad = math.radians(self.coordinates.latitude)
        
        cos_hour_angle = (
            (math.sin(math.radians(-refraction)) - 
             math.sin(lat_rad) * math.sin(declination)) /
            (math.cos(lat_rad) * math.cos(declination))
        )
        
        if cos_hour_angle < -1 or cos_hour_angle > 1:
            self.transit = 12 - self.coordinates.longitude / 15 - eot / 60
            self.sunrise = float('nan')
            self.sunset = float('nan')
            return
        
        hour_angle = math.acos(cos_hour_angle)
        
        self.transit = 12 - self.coordinates.longitude / 15 - eot / 60
        self.sunrise = self.transit - math.degrees(hour_angle) / 15
        self.sunset = self.transit + math.degrees(hour_angle) / 15
    
    def time_for_angle(self, angle: float, after_transit: bool) -> float:
        """计算太阳在指定角度时的时间"""
        jd = self.julian_day()
        n = jd - 2451545.0
        
        L = (280.460 + 0.9856474 * n) % 360.0
        g = (357.528 + 0.9856003 * n) % 360.0
        lambda_sun = (L + 1.915 * math.sin(math.radians(g)) + 
                     0.020 * math.sin(math.radians(2 * g))) % 360.0
        
        obliquity = 23.439 - 0.0000004 * n
        declination = math.asin(math.sin(math.radians(obliquity)) * 
                               math.sin(math.radians(lambda_sun)))
        
        right_ascension = math.atan2(
            math.cos(math.radians(obliquity)) * math.sin(math.radians(lambda_sun)),
            math.cos(math.radians(lambda_sun))
        )
        
        eot = 4 * (L - 0.0057183 - math.degrees(right_ascension))
        if eot > 20:
            eot -= 1440
        elif eot < -20:
            eot += 1440
        
        lat_rad = math.radians(self.coordinates.latitude)
        angle_rad = math.radians(angle)
        
        cos_hour_angle = (
            (math.sin(angle_rad) - math.sin(lat_rad) * math.sin(declination)) /
            (math.cos(lat_rad) * math.cos(declination))
        )
        
        if cos_hour_angle < -1 or cos_hour_angle > 1:
            return float('nan')
        
        hour_angle = math.acos(cos_hour_angle)
        transit = 12 - self.coordinates.longitude / 15 - eot / 60
        
        if after_transit:
            return transit + math.degrees(hour_angle) / 15
        else:
            return transit - math.degrees(hour_angle) / 15
    
    def asr_time(self, shadow_factor: float = 1.0) -> float:
        """Asr时间计算"""
        jd = self.julian_day()
        n = jd - 2451545.0
        
        L = (280.460 + 0.9856474 * n) % 360.0
        g = (357.528 + 0.9856003 * n) % 360.0
        lambda_sun = (L + 1.915 * math.sin(math.radians(g)) + 
                     0.020 * math.sin(math.radians(2 * g))) % 360.0
        
        obliquity = 23.439 - 0.0000004 * n
        declination = math.asin(math.sin(math.radians(obliquity)) * 
                               math.sin(math.radians(lambda_sun)))
        
        lat_rad = math.radians(self.coordinates.latitude)
        declination_lat_diff = abs(declination - lat_rad)
        asr_altitude = math.atan(1.0 / (shadow_factor + math.tan(declination_lat_diff)))
        asr_angle = math.degrees(asr_altitude)
        
        return self.time_for_angle(-asr_angle, True)

def hours_to_time(hours: float, date: datetime.date, timezone: datetime.timezone) -> datetime.datetime:
    """将小时转换为datetime对象"""
    if math.isnan(hours):
        return datetime.datetime.min.replace(tzinfo=timezone)
    
    hours = hours % 24
    h = int(hours)
    minutes_float = (hours - h) * 60
    m = round(minutes_float)
    
    if m >= 60:
        h += 1
        m = 0
    if h >= 24:
        h = 0
    
    return datetime.datetime.combine(date, datetime.time(h, m, 0)).replace(tzinfo=timezone)

def calculate_prayer_times(date: datetime.date, coordinates: Coordinates, 
                         timezone: datetime.timezone) -> PrayerTimes:
    """计算祈祷时间"""
    calc = SolarCalculator(date, coordinates)
    
    fajr_angle = 18.0
    isha_angle = 17.0
    
    subuh_time = calc.time_for_angle(-fajr_angle, False)
    terbit_time = calc.sunrise
    dhuha_time = calc.time_for_angle(4.5, False)
    zuhur_time = calc.transit
    ashar_time = calc.asr_time(1.0)
    maghrib_time = calc.sunset
    isya_time = calc.time_for_angle(-isha_angle, True)
    imsak_time = subuh_time - 10/60
    
    return PrayerTimes(
        imsak=hours_to_time(imsak_time, date, timezone),
        subuh=hours_to_time(subuh_time, date, timezone),
        terbit=hours_to_time(terbit_time, date, timezone),
        dhuha=hours_to_time(dhuha_time, date, timezone),
        zuhur=hours_to_time(zuhur_time, date, timezone),
        ashar=hours_to_time(ashar_time, date, timezone),
        maghrib=hours_to_time(maghrib_time, date, timezone),
        isya=hours_to_time(isya_time, date, timezone)
    )

def get_timezone(timezone_name: str) -> datetime.timezone:
    """获取时区对象"""
    if zoneinfo:
        try:
            return zoneinfo.ZoneInfo(timezone_name)
        except:
            pass
    
    # 回退映射
    timezone_map = {
        "Asia/Shanghai": datetime.timezone(datetime.timedelta(hours=8)),
        "Asia/Jakarta": datetime.timezone(datetime.timedelta(hours=7)),
        "Asia/Riyadh": datetime.timezone(datetime.timedelta(hours=3)),
        "Asia/Dubai": datetime.timezone(datetime.timedelta(hours=4)),
        "Europe/London": datetime.timezone(datetime.timedelta(hours=0)),
        "America/New_York": datetime.timezone(datetime.timedelta(hours=-5)),
    }
    return timezone_map.get(timezone_name, datetime.timezone.utc)

def format_time(dt: datetime.datetime) -> str:
    """格式化时间显示"""
    if dt == datetime.datetime.min.replace(tzinfo=dt.tzinfo):
        return "N/A"
    return dt.strftime("%H:%M")

if __name__ == "__main__":
    print("=== Jadwal Shalat (基于Flutter源码逆向分析) ===")
    print("Method: Muslim World League (Subuh -18.0°, Isha -17.0°)")
    print()
    
    # 获取用户输入
    try:
        lat = float(input("请输入纬度 (例如: 23.118889): ") or "23.118889")
        lon = float(input("请输入经度 (例如: 113.37): ") or "113.37")
        tz_name = input("请输入时区 (例如: Asia/Shanghai): ") or "Asia/Shanghai"
        
        coordinates = Coordinates(lat, lon)
        timezone = get_timezone(tz_name)
        date = datetime.date.today()
        
        times = calculate_prayer_times(date, coordinates, timezone)
        
        print()
        print(f"Tanggal: {date.strftime('%Y-%m-%d')}")
        print(f"Lokasi: {coordinates.latitude:.6f}, {coordinates.longitude:.6f}")
        print(f"Timezone: {tz_name}")
        print()
        print("Jadwal Shalat:")
        print(f"  Imsak:   {format_time(times.imsak)}")
        print(f"  Subuh:   {format_time(times.subuh)}")
        print(f"  Terbit:  {format_time(times.terbit)}")
        print(f"  Dhuha:   {format_time(times.dhuha)}")
        print(f"  Zuhur:   {format_time(times.zuhur)}")
        print(f"  Ashar:   {format_time(times.ashar)}")
        print(f"  Maghrib: {format_time(times.maghrib)}")
        print(f"  Isya:    {format_time(times.isya)}")
        
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"错误: {e}")
        print("使用默认值...")
        
        coordinates = Coordinates(23.118889, 113.37)
        timezone = get_timezone("Asia/Shanghai")
        date = datetime.date.today()
        
        times = calculate_prayer_times(date, coordinates, timezone)
        
        print()
        print(f"Tanggal: {date.strftime('%Y-%m-%d')}")
        print(f"Lokasi: {coordinates.latitude:.6f}, {coordinates.longitude:.6f}")
        print(f"Timezone: Asia/Shanghai")
        print()
        print("Jadwal Shalat:")
        print(f"  Imsak:   {format_time(times.imsak)}")
        print(f"  Subuh:   {format_time(times.subuh)}")
        print(f"  Terbit:  {format_time(times.terbit)}")
        print(f"  Dhuha:   {format_time(times.dhuha)}")
        print(f"  Zuhur:   {format_time(times.zuhur)}")
        print(f"  Ashar:   {format_time(times.ashar)}")
        print(f"  Maghrib: {format_time(times.maghrib)}")
        print(f"  Isya:    {format_time(times.isya)}")
